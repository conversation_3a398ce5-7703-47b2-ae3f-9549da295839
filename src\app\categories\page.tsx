'use client';

import React, { useState, useMemo, useEffect, Suspense } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { useCategories } from '@/hooks/useCategories';
import {
  MagnifyingGlassIcon,
  TagIcon,
  ArrowRightIcon,
  PlusIcon,
  FolderPlusIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { debounce } from '@/utils/helpers';
import CreateCategoryModal from '@/components/Categories/CreateCategoryModal';
import CategoryTree from '@/components/Categories/CategoryTree';
import { Category } from '@/types';

function CategoriesContent() {
  const { user } = useAuth();
  const { categories, loading, error, refetch } = useCategories();
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [isAutoApproved, setIsAutoApproved] = useState(false);
  const [parentCategoryForModal, setParentCategoryForModal] = useState<Category | undefined>();
  const [viewMode, setViewMode] = useState<'grid' | 'tree'>('grid');

  const searchParams = useSearchParams();
  const router = useRouter();

  // Check for success message from URL params
  useEffect(() => {
    const submitted = searchParams.get('submitted');
    const autoApproved = searchParams.get('autoApproved');

    if (submitted === 'true') {
      setShowSuccessMessage(true);
      setIsAutoApproved(autoApproved === 'true');

      // Clear URL params
      router.replace('/categories', { scroll: false });

      // Hide message after 5 seconds
      setTimeout(() => {
        setShowSuccessMessage(false);
      }, 5000);
    }
  }, [searchParams, router]);

  // Debounced search to avoid excessive filtering
  const debouncedSearch = useMemo(
    () => debounce((term: string) => setSearchTerm(term), 300),
    []
  );

  // Filter categories based on search term
  const filteredCategories = useMemo(() => {
    if (!searchTerm) return categories;
    
    return categories.filter(category =>
      category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      category.description?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [categories, searchTerm]);

  const handleCategoryCreated = () => {
    refetch();
  };

  const handleCreateSubcategory = (parentCategory: Category) => {
    setParentCategoryForModal(parentCategory);
    setShowCreateModal(true);
  };

  const handleCloseModal = () => {
    setShowCreateModal(false);
    setParentCategoryForModal(undefined);
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Fehler beim Laden</h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">

        {/* Success Message */}
        {showSuccessMessage && (
          <div className={`mb-6 p-4 rounded-lg border ${
            isAutoApproved
              ? 'bg-green-50 border-green-200'
              : 'bg-blue-50 border-blue-200'
          }`}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                {isAutoApproved ? (
                  <CheckCircleIcon className="h-5 w-5 text-green-400" />
                ) : (
                  <ClockIcon className="h-5 w-5 text-blue-400" />
                )}
              </div>
              <div className="ml-3">
                <h3 className={`text-sm font-medium ${
                  isAutoApproved ? 'text-green-800' : 'text-blue-800'
                }`}>
                  {isAutoApproved ? 'Link erfolgreich veröffentlicht!' : 'Link eingereicht!'}
                </h3>
                <p className={`text-sm ${
                  isAutoApproved ? 'text-green-700' : 'text-blue-700'
                }`}>
                  {isAutoApproved
                    ? 'Dein YouTube-Link wurde automatisch genehmigt und ist jetzt für alle sichtbar.'
                    : 'Dein Link wird geprüft und in Kürze veröffentlicht. Du erhältst eine Benachrichtigung, sobald er genehmigt wurde.'
                  }
                </p>
              </div>
              <div className="ml-auto">
                <button
                  onClick={() => setShowSuccessMessage(false)}
                  className={`text-sm font-medium ${
                    isAutoApproved
                      ? 'text-green-600 hover:text-green-500'
                      : 'text-blue-600 hover:text-blue-500'
                  }`}
                >
                  ×
                </button>
              </div>
            </div>
          </div>
        )}
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Alle Kategorien
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-6">
            Entdecke kuratierte Links zu verschiedenen Themenbereichen
          </p>

          {/* Action Buttons */}
          {user && (
            <div className="flex justify-center space-x-4">
              <button
                onClick={() => setShowCreateModal(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
              >
                <FolderPlusIcon className="h-4 w-4 mr-2" />
                Neue Kategorie erstellen
              </button>

              {/* View Mode Toggle */}
              <div className="flex rounded-md shadow-sm">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`px-4 py-2 text-sm font-medium rounded-l-md border ${
                    viewMode === 'grid'
                      ? 'bg-blue-600 text-white border-blue-600'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  Raster
                </button>
                <button
                  onClick={() => setViewMode('tree')}
                  className={`px-4 py-2 text-sm font-medium rounded-r-md border-t border-r border-b ${
                    viewMode === 'tree'
                      ? 'bg-blue-600 text-white border-blue-600'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  Baum
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Search Bar */}
        <div className="max-w-md mx-auto mb-8">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Kategorien durchsuchen..."
              className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              onChange={(e) => debouncedSearch(e.target.value)}
            />
          </div>
        </div>

        {/* Categories Grid */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[...Array(12)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-white rounded-lg border border-gray-200 p-6">
                  <div className="h-6 bg-gray-200 rounded w-3/4 mb-3"></div>
                  <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3 mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                </div>
              </div>
            ))}
          </div>
        ) : filteredCategories.length > 0 ? (
          <>
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {filteredCategories.map((category) => (
                  <Link
                    key={category.id}
                    href={`/category/${category.slug}`}
                    className="group bg-white rounded-lg border border-gray-200 p-6 hover:border-blue-300 hover:shadow-lg transition-all duration-200"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <TagIcon className="h-6 w-6 text-blue-600 group-hover:text-blue-700 flex-shrink-0" />
                        <div className="min-w-0">
                          <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 truncate">
                            {category.name}
                          </h3>
                          {category.level > 0 && (
                            <p className="text-xs text-gray-500">
                              Unterkategorie
                            </p>
                          )}
                        </div>
                      </div>
                      <ArrowRightIcon className="h-4 w-4 text-gray-400 group-hover:text-blue-600 group-hover:translate-x-1 transition-all flex-shrink-0" />
                    </div>

                    {category.description && (
                      <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                        {category.description}
                      </p>
                    )}

                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">
                        {category.totalLinks || 0} {category.totalLinks === 1 ? 'Link' : 'Links'}
                      </span>

                      {category.color && (
                        <div
                          className="w-4 h-4 rounded-full border border-gray-200"
                          style={{ backgroundColor: category.color }}
                        />
                      )}
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <CategoryTree
                categories={filteredCategories}
                onCreateSubcategory={handleCreateSubcategory}
                showCreateButton={!!user}
                currentUserId={user?.id}
              />
            )}

            {/* Stats */}
            <div className="mt-12 text-center">
              <p className="text-gray-600">
                {filteredCategories.length} von {categories.length} Kategorien angezeigt
              </p>
            </div>
          </>
        ) : (
          <div className="text-center py-12">
            <TagIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchTerm ? 'Keine Kategorien gefunden' : 'Noch keine Kategorien vorhanden'}
            </h3>
            <p className="text-gray-600 mb-6">
              {searchTerm 
                ? `Deine Suche nach "${searchTerm}" ergab keine Treffer.`
                : 'Erstelle die erste Kategorie mit einem Link!'
              }
            </p>
            <Link
              href="/submit"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              <PlusIcon className="h-5 w-5 mr-2" />
              Link einreichen
            </Link>
          </div>
        )}

        {/* Create Category Modal */}
        <CreateCategoryModal
          isOpen={showCreateModal}
          onClose={handleCloseModal}
          onSuccess={handleCategoryCreated}
          parentCategory={parentCategoryForModal}
        />
      </div>
    </div>
  );
}

export default function CategoriesPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Kategorien werden geladen...</p>
        </div>
      </div>
    }>
      <CategoriesContent />
    </Suspense>
  );
}