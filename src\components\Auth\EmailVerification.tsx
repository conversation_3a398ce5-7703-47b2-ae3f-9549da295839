'use client';

import React, { useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import {
  EnvelopeIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

interface EmailVerificationProps {
  email?: string;
  showTitle?: boolean;
  className?: string;
}

export const EmailVerification: React.FC<EmailVerificationProps> = ({
  email,
  showTitle = true,
  className = ''
}) => {
  const { sendVerificationEmail, checkEmailVerified, isEmailVerified } = useAuth();
  const [isResending, setIsResending] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [resendSuccess, setResendSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleResendEmail = async () => {
    try {
      setIsResending(true);
      setError(null);
      setResendSuccess(false);
      
      await sendVerificationEmail();
      setResendSuccess(true);
      
      // Reset success message after 5 seconds
      setTimeout(() => setResendSuccess(false), 5000);
      
    } catch (error: any) {
      setError(error.message || 'Fehler beim Senden der E-Mail');
    } finally {
      setIsResending(false);
    }
  };

  const handleCheckVerification = async () => {
    try {
      setIsChecking(true);
      setError(null);
      
      const verified = await checkEmailVerified();
      
      if (verified) {
        // Reload the page to update the auth state
        window.location.reload();
      } else {
        setError('E-Mail ist noch nicht verifiziert. Bitte überprüfe dein Postfach.');
      }
      
    } catch (error: any) {
      setError('Fehler beim Prüfen der Verifizierung');
    } finally {
      setIsChecking(false);
    }
  };

  if (isEmailVerified) {
    return (
      <div className={`rounded-md bg-green-50 p-4 ${className}`}>
        <div className="flex">
          <div className="flex-shrink-0">
            <CheckCircleIcon className="h-5 w-5 text-green-400" />
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-green-800">
              E-Mail-Adresse verifiziert
            </p>
            <p className="mt-1 text-sm text-green-700">
              Deine E-Mail-Adresse wurde erfolgreich verifiziert.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {showTitle && (
        <div className="text-center">
          <EnvelopeIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h2 className="mt-2 text-lg font-medium text-gray-900">
            E-Mail-Adresse verifizieren
          </h2>
        </div>
      )}

      <div className="rounded-md bg-yellow-50 p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">
              E-Mail-Verifizierung erforderlich
            </h3>
            <div className="mt-2 text-sm text-yellow-700">
              <p>
                Wir haben eine Verifizierungs-E-Mail an{' '}
                <span className="font-medium">{email}</span> gesendet.
              </p>
              <p className="mt-1">
                Bitte klicke auf den Link in der E-Mail, um dein Konto zu aktivieren.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Success Message */}
      {resendSuccess && (
        <div className="rounded-md bg-green-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <CheckCircleIcon className="h-5 w-5 text-green-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-800">
                Verifizierungs-E-Mail wurde erneut gesendet!
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-3">
        <button
          type="button"
          onClick={handleResendEmail}
          disabled={isResending}
          className="flex-1 flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isResending ? (
            <>
              <ArrowPathIcon className="animate-spin -ml-1 mr-2 h-4 w-4" />
              Wird gesendet...
            </>
          ) : (
            <>
              <EnvelopeIcon className="-ml-1 mr-2 h-4 w-4" />
              E-Mail erneut senden
            </>
          )}
        </button>

        <button
          type="button"
          onClick={handleCheckVerification}
          disabled={isChecking}
          className="flex-1 flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isChecking ? (
            <>
              <ArrowPathIcon className="animate-spin -ml-1 mr-2 h-4 w-4" />
              Wird geprüft...
            </>
          ) : (
            <>
              <CheckCircleIcon className="-ml-1 mr-2 h-4 w-4" />
              Verifizierung prüfen
            </>
          )}
        </button>
      </div>

      <div className="text-center">
        <p className="text-sm text-gray-600">
          Keine E-Mail erhalten? Überprüfe auch deinen Spam-Ordner.
        </p>
      </div>
    </div>
  );
};

export default EmailVerification;
