'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { categoryService } from '@/firebase/firestore';
import { CreateCategoryForm, Category } from '@/types';
import { XMarkIcon, FolderPlusIcon, FolderIcon } from '@heroicons/react/24/outline';

interface CreateCategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  parentCategory?: Category; // If provided, creates a subcategory
}

const CreateCategoryModal: React.FC<CreateCategoryModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  parentCategory
}) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [availableCategories, setAvailableCategories] = useState<Category[]>([]);
  const [form, setForm] = useState<CreateCategoryForm>({
    name: '',
    description: '',
    color: '#3B82F6',
    icon: 'FolderIcon',
    parentId: parentCategory?.id
  });

  // Load available root categories for subcategory selection
  useEffect(() => {
    if (isOpen && !parentCategory) {
      loadRootCategories();
    }
    // Clear error when modal opens
    if (isOpen) {
      setError('');
    }
  }, [isOpen, parentCategory]);

  const loadRootCategories = async () => {
    try {
      const categories = await categoryService.getRootCategories();
      setAvailableCategories(categories);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(''); // Clear previous errors

    if (!user) {
      setError('Du musst angemeldet sein, um eine Kategorie zu erstellen.');
      return;
    }

    if (!form.name.trim()) {
      setError('Kategorie-Name ist erforderlich');
      return;
    }

    try {
      setLoading(true);
      await categoryService.create(form, user.id);

      // Reset form
      setForm({
        name: '',
        description: '',
        color: '#3B82F6',
        icon: 'FolderIcon',
        parentId: parentCategory?.id
      });

      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error creating category:', error);
      // Show the specific error message from the service
      const errorMessage = error instanceof Error ? error.message : 'Fehler beim Erstellen der Kategorie. Bitte versuche es erneut.';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setForm({
        name: '',
        description: '',
        color: '#3B82F6',
        icon: 'FolderIcon',
        parentId: parentCategory?.id
      });
      setError(''); // Clear error when closing
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <FolderPlusIcon className="h-6 w-6 text-blue-600" />
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                {parentCategory ? 'Neue Unterkategorie erstellen' : 'Neue Kategorie erstellen'}
              </h2>
              {parentCategory && (
                <p className="text-sm text-gray-600">
                  in "{parentCategory.name}"
                </p>
              )}
            </div>
          </div>
          <button
            onClick={handleClose}
            disabled={loading}
            className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          {/* Error Message */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          <div className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Name *
              </label>
              <input
                type="text"
                id="name"
                required
                maxLength={50}
                value={form.name}
                onChange={(e) => setForm(prev => ({ ...prev, name: e.target.value }))}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                placeholder="z.B. Web Development"
                disabled={loading}
              />
              <p className="mt-1 text-xs text-gray-500">
                {form.name.length}/50 Zeichen
              </p>
            </div>

            {/* Parent Category Selection (only if not creating subcategory) */}
            {!parentCategory && (
              <div>
                <label htmlFor="parentId" className="block text-sm font-medium text-gray-700 mb-1">
                  Übergeordnete Kategorie (optional)
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FolderIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <select
                    id="parentId"
                    value={form.parentId || ''}
                    onChange={(e) => setForm(prev => ({ ...prev, parentId: e.target.value || undefined }))}
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                    disabled={loading}
                  >
                    <option value="">Hauptkategorie erstellen</option>
                    {availableCategories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  Wähle eine bestehende Kategorie, um eine Unterkategorie zu erstellen
                </p>
              </div>
            )}

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                Beschreibung (optional)
              </label>
              <textarea
                id="description"
                rows={3}
                maxLength={200}
                value={form.description}
                onChange={(e) => setForm(prev => ({ ...prev, description: e.target.value }))}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                placeholder="Beschreibe, worum es in dieser Kategorie geht..."
                disabled={loading}
              />
              <p className="mt-1 text-xs text-gray-500">
                {form.description.length}/200 Zeichen
              </p>
            </div>

            <div>
              <label htmlFor="color" className="block text-sm font-medium text-gray-700 mb-1">
                Farbe
              </label>
              <div className="flex items-center space-x-3">
                <input
                  type="color"
                  id="color"
                  value={form.color}
                  onChange={(e) => setForm(prev => ({ ...prev, color: e.target.value }))}
                  className="h-10 w-16 border border-gray-300 rounded cursor-pointer disabled:opacity-50"
                  disabled={loading}
                />
                <span className="text-sm text-gray-600">{form.color}</span>
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              onClick={handleClose}
              disabled={loading}
              className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 transition-colors"
            >
              Abbrechen
            </button>
            <button
              type="submit"
              disabled={loading || !form.name.trim()}
              className="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? 'Erstelle...' : 'Kategorie erstellen'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateCategoryModal;
