'use client';

import React from 'react';
import Link from 'next/link';
import {
  CheckCircleIcon,
  EnvelopeIcon,
  ArrowLeftIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

interface PasswordResetSuccessProps {
  email: string;
  onResend?: () => void;
  className?: string;
}

export const PasswordResetSuccess: React.FC<PasswordResetSuccessProps> = ({
  email,
  onResend,
  className = ''
}) => {
  return (
    <div className={`space-y-6 ${className}`}>
      <div className="rounded-md bg-green-50 p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <CheckCircleIcon className="h-5 w-5 text-green-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-green-800">
              Passwort-Reset-E-Mail gesendet
            </h3>
            <div className="mt-2 text-sm text-green-700">
              <p>
                Wir haben eine E-Mail an{' '}
                <span className="font-medium">{email}</span> gesendet.
              </p>
              <p className="mt-1">
                Klicke auf den Link in der E-Mail, um dein Passwort zurückzusetzen.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Instructions */}
      <div className="bg-blue-50 rounded-md p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <EnvelopeIcon className="h-5 w-5 text-blue-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              Nächste Schritte
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <ol className="list-decimal list-inside space-y-1">
                <li>Überprüfe dein E-Mail-Postfach</li>
                <li>Klicke auf den "Passwort zurücksetzen" Link</li>
                <li>Gib dein neues Passwort ein</li>
                <li>Melde dich mit dem neuen Passwort an</li>
              </ol>
            </div>
          </div>
        </div>
      </div>

      {/* Important Notes */}
      <div className="bg-yellow-50 rounded-md p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <ClockIcon className="h-5 w-5 text-yellow-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">
              Wichtige Hinweise
            </h3>
            <div className="mt-2 text-sm text-yellow-700">
              <ul className="list-disc list-inside space-y-1">
                <li>Die E-Mail kann bis zu 5 Minuten dauern</li>
                <li>Überprüfe auch deinen Spam-Ordner</li>
                <li>Der Reset-Link ist 1 Stunde gültig</li>
                <li>Du kannst den Link nur einmal verwenden</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col space-y-3">
        {onResend && (
          <button
            onClick={onResend}
            className="w-full flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            <EnvelopeIcon className="-ml-1 mr-2 h-4 w-4" />
            E-Mail erneut senden
          </button>
        )}

        <Link
          href="/login"
          className="w-full flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
        >
          <ArrowLeftIcon className="-ml-1 mr-2 h-4 w-4" />
          Zurück zur Anmeldung
        </Link>
      </div>

      {/* Help Text */}
      <div className="text-center">
        <p className="text-xs text-gray-500">
          Probleme beim Zurücksetzen?{' '}
          <Link 
            href="/contact" 
            className="text-blue-600 hover:text-blue-500 font-medium"
          >
            Kontaktiere uns
          </Link>
        </p>
      </div>
    </div>
  );
};

export default PasswordResetSuccess;
