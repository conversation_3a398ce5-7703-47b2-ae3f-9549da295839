/**
 * Migration script to add hierarchical fields to existing categories
 * This should be run once to update existing categories in the database
 */

import { 
  collection, 
  getDocs, 
  updateDoc, 
  doc, 
  writeBatch 
} from 'firebase/firestore';
import { db } from '@/firebase/config';
import slugify from 'slugify';

export const migrateCategoriesHierarchy = async () => {
  console.log('🔄 Starting category hierarchy migration...');
  
  try {
    // Get all existing categories
    const categoriesRef = collection(db, 'categories');
    const snapshot = await getDocs(categoriesRef);
    
    if (snapshot.empty) {
      console.log('✅ No categories found to migrate');
      return;
    }

    const batch = writeBatch(db);
    let updateCount = 0;

    snapshot.docs.forEach((docSnapshot) => {
      const category = docSnapshot.data();
      const categoryRef = doc(db, 'categories', docSnapshot.id);
      
      // Check if category already has hierarchical fields
      if (category.level !== undefined && category.path !== undefined) {
        console.log(`⏭️  Skipping ${category.name} - already migrated`);
        return;
      }

      // Generate slug if missing
      const slug = category.slug || slugify(category.name, { lower: true, strict: true });
      
      // Add hierarchical fields for root categories
      const updates = {
        level: 0, // All existing categories become root categories
        path: slug,
        parentId: null,
        totalSubcategories: 0,
        ...(category.slug ? {} : { slug }) // Only add slug if missing
      };

      batch.update(categoryRef, updates);
      updateCount++;
      
      console.log(`📝 Queued update for: ${category.name}`);
    });

    if (updateCount > 0) {
      await batch.commit();
      console.log(`✅ Successfully migrated ${updateCount} categories`);
    } else {
      console.log('✅ All categories already migrated');
    }

  } catch (error) {
    console.error('❌ Error migrating categories:', error);
    throw error;
  }
};

// Helper function to run migration from browser console
export const runMigration = async () => {
  try {
    await migrateCategoriesHierarchy();
    console.log('🎉 Migration completed successfully!');
  } catch (error) {
    console.error('💥 Migration failed:', error);
  }
};
