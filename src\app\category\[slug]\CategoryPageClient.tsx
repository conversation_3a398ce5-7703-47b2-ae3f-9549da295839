'use client';

import React, { useState } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { useCategoryBySlugQuery } from '@/hooks/queries/useCategoriesQuery';
import { useCategoryLinksQuery } from '@/hooks/queries/useLinksQuery';
import { useAuth } from '@/context/AuthContext';
import LinkCard from '@/components/UI/LinkCard';
import {
  ArrowLeftIcon,
  TagIcon,
  PlusIcon,
  FunnelIcon,
  EyeIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';
import { FilterOptions } from '@/types';

export default function CategoryPageClient() {
  const params = useParams();
  const slug = params.slug as string;
  const { user } = useAuth();
  
  const [filters, setFilters] = useState<FilterOptions>({
    sortBy: 'newest',
    timeRange: 'all'
  });
  const [showFilters, setShowFilters] = useState(false);

  // Fetch category data
  const {
    data: category,
    isLoading: categoryLoading,
    error: categoryError
  } = useCategoryBySlugQuery(slug);

  // Fetch links for this category
  const {
    data: linksData,
    isLoading: linksLoading,
    error: linksError
  } = useCategoryLinksQuery(category?.id || '', 50);

  if (categoryLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Kategorie wird geladen...</p>
        </div>
      </div>
    );
  }

  if (categoryError || !category) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <TagIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Kategorie nicht gefunden</h2>
          <p className="text-gray-600 mb-6">
            Die angeforderte Kategorie existiert nicht oder wurde entfernt.
          </p>
          <Link
            href="/categories"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Zurück zu den Kategorien
          </Link>
        </div>
      </div>
    );
  }

  const links = linksData?.data || [];

  // Sort links based on filters
  const sortedLinks = [...links].sort((a, b) => {
    switch (filters.sortBy) {
      case 'newest':
        return new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime();
      case 'oldest':
        return new Date(a.submittedAt).getTime() - new Date(b.submittedAt).getTime();
      case 'rating':
        return (b.averageRating || 0) - (a.averageRating || 0);
      case 'popular':
        return (b.totalComments || 0) - (a.totalComments || 0);
      default:
        return 0;
    }
  });

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Breadcrumb */}
        <div className="mb-6">
          <nav className="flex items-center space-x-2 text-sm text-gray-500">
            <Link href="/" className="hover:text-gray-700">
              Startseite
            </Link>
            <span>/</span>
            <Link href="/categories" className="hover:text-gray-700">
              Kategorien
            </Link>
            <span>/</span>
            <span className="text-gray-900 font-medium">{category.name}</span>
          </nav>
        </div>

        {/* Category Header */}
        <div className="bg-white rounded-lg border border-gray-200 p-8 mb-8">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-4">
                <div 
                  className="w-12 h-12 rounded-lg flex items-center justify-center"
                  style={{ backgroundColor: category.color || '#3B82F6' }}
                >
                  <TagIcon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">{category.name}</h1>
                  {category.level > 0 && (
                    <p className="text-sm text-gray-500 mt-1">Unterkategorie</p>
                  )}
                </div>
              </div>
              
              {category.description && (
                <p className="text-lg text-gray-600 mb-6">{category.description}</p>
              )}

              <div className="flex items-center space-x-6 text-sm text-gray-500">
                <div className="flex items-center space-x-1">
                  <EyeIcon className="h-4 w-4" />
                  <span>{category.totalLinks || 0} {category.totalLinks === 1 ? 'Link' : 'Links'}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <CalendarIcon className="h-4 w-4" />
                  <span>Erstellt am {new Date(category.createdAt).toLocaleDateString('de-DE')}</span>
                </div>
              </div>
            </div>

            {user && (
              <Link
                href="/submit"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Link hinzufügen
              </Link>
            )}
          </div>
        </div>

        {/* Filters */}
        <div className="mb-6">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
          >
            <FunnelIcon className="h-4 w-4 mr-2" />
            Sortierung
            {filters.sortBy !== 'newest' && (
              <span className="ml-2 px-2 py-0.5 bg-blue-100 text-blue-800 text-xs rounded-full">
                Aktiv
              </span>
            )}
          </button>

          {showFilters && (
            <div className="mt-4 bg-white rounded-lg border border-gray-200 p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sortieren nach
                  </label>
                  <select
                    value={filters.sortBy}
                    onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value as any }))}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="newest">Neueste zuerst</option>
                    <option value="oldest">Älteste zuerst</option>
                    <option value="rating">Beste Bewertung</option>
                    <option value="popular">Beliebteste</option>
                  </select>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Links */}
        <div>
          {linksLoading ? (
            <div className="space-y-6">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-white rounded-lg border border-gray-200 p-6">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                    <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : linksError ? (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="text-red-800">
                <strong>Fehler:</strong> {linksError.message}
              </div>
            </div>
          ) : sortedLinks.length > 0 ? (
            <div className="space-y-6">
              {sortedLinks.map((link) => (
                <LinkCard
                  key={link.id}
                  link={link}
                  showCategory={false}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12 bg-white rounded-lg border border-gray-200">
              <TagIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Noch keine Links in dieser Kategorie
              </h3>
              <p className="text-gray-600 mb-6">
                Sei der Erste und füge einen Link zu dieser Kategorie hinzu!
              </p>
              {user && (
                <Link
                  href="/submit"
                  className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                >
                  <PlusIcon className="h-5 w-5 mr-2" />
                  Link hinzufügen
                </Link>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
