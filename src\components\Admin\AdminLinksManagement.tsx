'use client';

import React from 'react';
import { LinkWithDetails } from '@/types';
import AdminLinkCard from './AdminLinkCard';
import BulkActions from './BulkActions';
import { CheckCircleIcon, LinkIcon } from '@heroicons/react/24/outline';
import { linkService } from '@/firebase/firestore';

interface AdminLinksManagementProps {
  pendingLinks: LinkWithDetails[];
  allLinks: LinkWithDetails[];
  selectedLinks: string[];
  onSelectLink: (linkId: string, selected: boolean) => void;
  onSelectAll: (links: LinkWithDetails[], selected: boolean) => void;
  onBulkAction: (action: string, linkIds: string[]) => void;
  onClearSelection: () => void;
  onApproveLink: (linkId: string) => void;
  onRejectLink: (linkId: string) => void;
}

const AdminLinksManagement: React.FC<AdminLinksManagementProps> = ({
  pendingLinks,
  allLinks,
  selectedLinks,
  onSelectLink,
  onSelectAll,
  onBulkAction,
  onClearSelection,
  onApproveLink,
  onRejectLink
}) => {
  const handleFeatureToggle = async (linkId: string, featured: boolean) => {
    try {
      await linkService.feature(linkId, featured);
      onBulkAction(featured ? 'feature' : 'unfeature', [linkId]);
    } catch (error) {
      console.error('Error toggling feature status:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* Pending Links Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">
            Wartende Links ({pendingLinks.length})
          </h3>
          {pendingLinks.length > 0 && (
            <div className="flex items-center space-x-2">
              <button
                onClick={() => onSelectAll(pendingLinks, !pendingLinks.every(link => selectedLinks.includes(link.id)))}
                className="text-sm text-blue-600 hover:text-blue-700 transition-colors"
              >
                {pendingLinks.every(link => selectedLinks.includes(link.id)) ? 'Alle abwählen' : 'Alle auswählen'}
              </button>
            </div>
          )}
        </div>

        {/* Bulk Actions for Pending Links */}
        {selectedLinks.some(id => pendingLinks.find(link => link.id === id)) && (
          <BulkActions
            selectedLinks={[...pendingLinks, ...allLinks].filter(link => selectedLinks.includes(link.id))}
            onAction={onBulkAction}
            onClearSelection={onClearSelection}
          />
        )}
        
        {pendingLinks.length === 0 ? (
          <div className="text-center py-8">
            <CheckCircleIcon className="h-12 w-12 text-green-400 mx-auto mb-4" />
            <p className="text-gray-600">Alle Links sind bearbeitet!</p>
          </div>
        ) : (
          <div className="space-y-4">
            {pendingLinks.map((link) => (
              <AdminLinkCard
                key={link.id}
                link={link}
                isSelected={selectedLinks.includes(link.id)}
                onSelect={onSelectLink}
                onApprove={onApproveLink}
                onReject={onRejectLink}
                showActions={true}
              />
            ))}
          </div>
        )}
      </div>

      {/* All Links Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">
            Alle Links ({allLinks.length})
          </h3>
          {allLinks.length > 0 && (
            <div className="flex items-center space-x-2">
              <button
                onClick={() => onSelectAll(allLinks, !allLinks.every(link => selectedLinks.includes(link.id)))}
                className="text-sm text-blue-600 hover:text-blue-700 transition-colors"
              >
                {allLinks.every(link => selectedLinks.includes(link.id)) ? 'Alle abwählen' : 'Alle auswählen'}
              </button>
            </div>
          )}
        </div>

        {/* Bulk Actions for All Links */}
        {selectedLinks.some(id => allLinks.find(link => link.id === id)) && (
          <BulkActions
            selectedLinks={[...pendingLinks, ...allLinks].filter(link => selectedLinks.includes(link.id))}
            onAction={onBulkAction}
            onClearSelection={onClearSelection}
          />
        )}
        
        {allLinks.length === 0 ? (
          <div className="text-center py-8">
            <LinkIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Noch keine Links vorhanden</p>
          </div>
        ) : (
          <div className="space-y-4">
            {allLinks.slice(0, 20).map((link) => (
              <AdminLinkCard
                key={link.id}
                link={link}
                isSelected={selectedLinks.includes(link.id)}
                onSelect={onSelectLink}
                onFeature={handleFeatureToggle}
                showActions={true}
              />
            ))}
            {allLinks.length > 20 && (
              <div className="text-center py-4">
                <p className="text-sm text-gray-500">
                  Zeige die ersten 20 von {allLinks.length} Links
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminLinksManagement;
