'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import {
  onAuthStateChanged,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  sendEmailVerification,
  reload,
  updateProfile,
  User as FirebaseUser
} from 'firebase/auth';
import { auth } from '@/firebase/config';
import { userService } from '@/firebase/firestore';
import { User, AuthState } from '@/types';

interface AuthContextType extends AuthState {
  // Auth actions
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, username: string, displayName: string) => Promise<void>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updateUserProfile: (data: { displayName?: string; bio?: string; website?: string }) => Promise<void>;

  // Email verification
  sendVerificationEmail: () => Promise<void>;
  checkEmailVerified: () => Promise<boolean>;
  isEmailVerified: boolean;

  // Utils
  refreshUser: () => Promise<void>;
  userIsAdmin: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Auth provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    loading: true,
    error: null
  });
  const [isEmailVerified, setIsEmailVerified] = useState<boolean>(false);

  // Debug: Log Firebase config
  useEffect(() => {
    console.log('🔥 Firebase Config Check:', {
      apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY ? 'Set' : 'Missing',
      authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN ? 'Set' : 'Missing',
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID ? 'Set' : 'Missing',
    });

    // Test Firebase connectivity
    import('@/firebase/config').then((firebase) => {
      console.log('🔥 Firebase initialized successfully');
      console.log('🔥 Auth instance:', firebase.auth);
      console.log('🔥 Firestore instance:', firebase.db);
    }).catch((error) => {
      console.error('🚨 Firebase initialization failed:', error);
    });
  }, []);

  // Helper to update error state
  const setError = (error: string | null) => {
    setAuthState(prev => ({ ...prev, error }));
  };

  // Helper to update loading state
  const setLoading = (loading: boolean) => {
    setAuthState(prev => ({ ...prev, loading }));
  };

  // Load user data from Firestore when Firebase user is available
  const loadUserData = async (firebaseUser: FirebaseUser): Promise<User | null> => {
    try {
      console.log('👤 Loading user data for Firebase user:', firebaseUser.uid);
      const userData = await userService.getById(firebaseUser.uid);
      console.log('👤 User data from Firestore:', userData);
      
      if (!userData) {
        console.log('👤 No user data found, creating new user...');
        // If user data doesn't exist in Firestore, create it
        const newUserData = {
          email: firebaseUser.email!,
          username: firebaseUser.email!.split('@')[0], // fallback username
          displayName: firebaseUser.displayName || firebaseUser.email!.split('@')[0],
          bio: '',
          website: '',
          isVerified: false,
          ...(firebaseUser.photoURL && { avatar: firebaseUser.photoURL })
        };
        
        console.log('👤 Creating user with data:', newUserData);
        await userService.create(newUserData, firebaseUser.uid);
        
        const createdUser = { 
          ...newUserData, 
          id: firebaseUser.uid, 
          createdAt: new Date(), 
          updatedAt: new Date() 
        };
        console.log('👤 User created successfully:', createdUser);
        return createdUser;
      }
      
      return userData;
    } catch (error) {
      console.error('🚨 Error loading user data:', error);
      throw error;
    }
  };

  // Sign in with email and password
  const signIn = async (email: string, password: string): Promise<void> => {
    try {
      setError(null);
      setLoading(true);
      console.log('🔐 Attempting to sign in with:', email);

      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      console.log('✅ Sign in successful:', userCredential.user.uid);

      // Check email verification status
      const emailVerified = userCredential.user.emailVerified;
      setIsEmailVerified(emailVerified);

      if (!emailVerified) {
        console.log('⚠️ E-Mail ist nicht verifiziert');
        throw new Error('Bitte verifiziere deine E-Mail-Adresse, bevor du dich anmeldest. Überprüfe dein E-Mail-Postfach.');
      }

    } catch (error: any) {
      console.error('🚨 Sign in failed:', error);
      
      let errorMessage = 'Anmeldung fehlgeschlagen';
      if (error.code === 'auth/user-not-found') {
        errorMessage = 'Kein Benutzer mit dieser E-Mail gefunden. Bitte registriere dich zuerst.';
      } else if (error.code === 'auth/wrong-password') {
        errorMessage = 'Falsches Passwort. Bitte versuche es erneut.';
      } else if (error.code === 'auth/invalid-credential') {
        errorMessage = 'Ungültige Anmeldedaten. Bitte überprüfe E-Mail und Passwort.';
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = 'Ungültige E-Mail-Adresse.';
      } else if (error.code === 'auth/user-disabled') {
        errorMessage = 'Dieses Konto wurde deaktiviert.';
      } else if (error.code === 'auth/too-many-requests') {
        errorMessage = 'Zu viele Anmeldeversuche. Bitte versuche es später erneut.';
      } else if (error.code === 'auth/network-request-failed') {
        errorMessage = 'Netzwerkfehler. Bitte überprüfe deine Internetverbindung.';
      } else if (error.code === 'auth/operation-not-allowed') {
        errorMessage = 'E-Mail/Passwort-Anmeldung ist nicht aktiviert.';
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Sign up with email and password
  const signUp = async (
    email: string,
    password: string,
    username: string,
    displayName: string
  ): Promise<void> => {
    try {
      setError(null);
      setLoading(true);

      // Check if username is already taken
      const usernameExists = await userService.checkUsernameExists(username);
      if (usernameExists) {
        throw new Error('Username is already taken');
      }

      // Create Firebase user
      const { user: firebaseUser } = await createUserWithEmailAndPassword(auth, email, password);

      // Update Firebase profile
      await updateProfile(firebaseUser, { displayName });

      // Send email verification
      await sendEmailVerification(firebaseUser);
      console.log('📧 Verifizierungs-E-Mail gesendet an:', email);

      // Create user document in Firestore
      const userData = {
        email,
        username,
        displayName,
        bio: '',
        website: '',
        isVerified: false
      };

      await userService.create(userData, firebaseUser.uid);

      // Sign out user until email is verified
      await signOut(auth);
      setIsEmailVerified(false);
      
    } catch (error: any) {
      setError(error.message || 'Failed to create account');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Sign out
  const logout = async (): Promise<void> => {
    try {
      setError(null);
      await signOut(auth);
      setAuthState({ user: null, loading: false, error: null });
    } catch (error: any) {
      setError(error.message || 'Failed to sign out');
      throw error;
    }
  };

  // Reset password
  const resetPassword = async (email: string): Promise<void> => {
    try {
      setError(null);
      await sendPasswordResetEmail(auth, email);
    } catch (error: any) {
      setError(error.message || 'Failed to send password reset email');
      throw error;
    }
  };

  // Send email verification
  const sendVerificationEmail = async (): Promise<void> => {
    try {
      setError(null);

      if (!auth.currentUser) {
        throw new Error('Kein Benutzer angemeldet');
      }

      if (auth.currentUser.emailVerified) {
        throw new Error('E-Mail ist bereits verifiziert');
      }

      await sendEmailVerification(auth.currentUser);
      console.log('📧 Verifizierungs-E-Mail gesendet');

    } catch (error: any) {
      console.error('🚨 Fehler beim Senden der Verifizierungs-E-Mail:', error);

      let errorMessage = 'Fehler beim Senden der Verifizierungs-E-Mail';
      if (error.code === 'auth/too-many-requests') {
        errorMessage = 'Zu viele Anfragen. Bitte warte einen Moment und versuche es erneut.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      setError(errorMessage);
      throw error;
    }
  };

  // Check if email is verified
  const checkEmailVerified = async (): Promise<boolean> => {
    try {
      if (!auth.currentUser) {
        return false;
      }

      // Reload user to get latest verification status
      await reload(auth.currentUser);
      const verified = auth.currentUser.emailVerified;
      setIsEmailVerified(verified);

      console.log('📧 E-Mail-Verifizierungsstatus:', verified);
      return verified;

    } catch (error: any) {
      console.error('🚨 Fehler beim Prüfen der E-Mail-Verifizierung:', error);
      return false;
    }
  };

  // Update user profile
  const updateUserProfile = async (data: {
    displayName?: string;
    bio?: string;
    website?: string;
  }): Promise<void> => {
    try {
      setError(null);
      
      if (!authState.user) {
        throw new Error('No user logged in');
      }

      // Update Firestore document
      await userService.update(authState.user.id, data);
      
      // Update local state
      setAuthState(prev => ({
        ...prev,
        user: prev.user ? { ...prev.user, ...data, updatedAt: new Date() } : null
      }));

      // Update Firebase profile if displayName changed
      if (data.displayName && auth.currentUser) {
        await updateProfile(auth.currentUser, { displayName: data.displayName });
      }
      
    } catch (error: any) {
      setError(error.message || 'Failed to update profile');
      throw error;
    }
  };

  // Refresh user data from Firestore
  const refreshUser = async (): Promise<void> => {
    try {
      if (!auth.currentUser) return;
      
      const userData = await loadUserData(auth.currentUser);
      setAuthState(prev => ({ ...prev, user: userData }));
    } catch (error: any) {
      setError(error.message || 'Failed to refresh user data');
    }
  };

  // Listen to auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      setLoading(true);
      setError(null);

      try {
        if (firebaseUser) {
          // User is signed in
          const userData = await loadUserData(firebaseUser);
          setIsEmailVerified(firebaseUser.emailVerified);
          setAuthState({
            user: userData,
            loading: false,
            error: null
          });
        } else {
          // User is signed out
          setIsEmailVerified(false);
          setAuthState({
            user: null,
            loading: false,
            error: null
          });
        }
      } catch (error: any) {
        setAuthState({
          user: null,
          loading: false,
          error: error.message || 'Authentication error'
        });
      }
    });

    return unsubscribe;
  }, []);

  const value: AuthContextType = {
    ...authState,
    signIn,
    signUp,
    logout,
    resetPassword,
    updateUserProfile,
    sendVerificationEmail,
    checkEmailVerified,
    isEmailVerified,
    refreshUser,
    userIsAdmin: authState.user?.isAdmin || false
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}; 