'use client';

import React, { useState } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { reportService } from '@/firebase/firestore';
import { useAuth } from '@/context/AuthContext';

interface ReportModalProps {
  isOpen: boolean;
  onClose: () => void;
  targetType: 'link' | 'comment';
  targetId: string;
  targetTitle?: string; // Title of link or snippet of comment
}

const reportReasons = [
  { value: 'spam', label: 'Spam oder Werbung' },
  { value: 'inappropriate', label: 'Unangemessener Inhalt' },
  { value: 'harassment', label: 'Belästigung oder Mobbing' },
  { value: 'copyright', label: 'Urheberrechtsverletzung' },
  { value: 'misinformation', label: 'Fehlinformation' },
  { value: 'other', label: 'Anderer Grund' }
];

const ReportModal: React.FC<ReportModalProps> = ({
  isOpen,
  onClose,
  targetType,
  targetId,
  targetTitle
}) => {
  const { user } = useAuth();
  const [selectedReason, setSelectedReason] = useState('');
  const [customReason, setCustomReason] = useState('');
  const [description, setDescription] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user || !selectedReason || isSubmitting) return;

    try {
      setIsSubmitting(true);
      
      await reportService.create(
        targetType,
        targetId,
        user.id,
        selectedReason,
        selectedReason === 'other' ? customReason : undefined,
        description || undefined
      );

      setSubmitted(true);
      
      // Reset form after short delay
      setTimeout(() => {
        setSubmitted(false);
        setSelectedReason('');
        setCustomReason('');
        setDescription('');
        onClose();
      }, 2000);

    } catch (error) {
      console.error('Error submitting report:', error);
      alert('Fehler beim Senden der Meldung. Bitte versuche es erneut.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setSelectedReason('');
      setCustomReason('');
      setDescription('');
      setSubmitted(false);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            {targetType === 'link' ? 'Link melden' : 'Kommentar melden'}
          </h2>
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {submitted ? (
            <div className="text-center py-8">
              <div className="mb-4">
                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                  <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Meldung gesendet</h3>
              <p className="text-gray-600">Vielen Dank! Wir werden deine Meldung prüfen.</p>
            </div>
          ) : (
            <>
              {targetTitle && (
                <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm font-medium text-gray-700">Du meldest:</p>
                  <p className="text-sm text-gray-600 truncate">{targetTitle}</p>
                </div>
              )}

              <form onSubmit={handleSubmit}>
                {/* Reason Selection */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Grund für die Meldung *
                  </label>
                  <div className="space-y-2">
                    {reportReasons.map((reason) => (
                      <label key={reason.value} className="flex items-center">
                        <input
                          type="radio"
                          name="reason"
                          value={reason.value}
                          checked={selectedReason === reason.value}
                          onChange={(e) => setSelectedReason(e.target.value)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                          disabled={isSubmitting}
                        />
                        <span className="ml-2 text-sm text-gray-700">{reason.label}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Custom Reason Input */}
                {selectedReason === 'other' && (
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Bitte beschreibe den Grund *
                    </label>
                    <input
                      type="text"
                      value={customReason}
                      onChange={(e) => setCustomReason(e.target.value)}
                      placeholder="Beschreibe kurz das Problem..."
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={isSubmitting}
                      required
                    />
                  </div>
                )}

                {/* Description */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Zusätzliche Details (optional)
                  </label>
                  <textarea
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Gib weitere Details an, die bei der Prüfung helfen könnten..."
                    rows={3}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={isSubmitting}
                  />
                </div>

                {/* Actions */}
                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={handleClose}
                    disabled={isSubmitting}
                    className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 disabled:bg-gray-50 disabled:cursor-not-allowed rounded-md transition-colors"
                  >
                    Abbrechen
                  </button>
                  <button
                    type="submit"
                    disabled={!selectedReason || isSubmitting || (selectedReason === 'other' && !customReason.trim())}
                    className="flex-1 px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed rounded-md transition-colors"
                  >
                    {isSubmitting ? 'Wird gesendet...' : 'Melden'}
                  </button>
                </div>
              </form>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default ReportModal; 