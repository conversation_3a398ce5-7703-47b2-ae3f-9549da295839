'use client';

import React, { useMemo } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { LinkWithDetails } from '@/types';
import LinkCard from './LinkCard';

interface VirtualizedLinkListProps {
  links: LinkWithDetails[];
  showCategory?: boolean;
  estimateSize?: number;
  overscan?: number;
  className?: string;
}

const VirtualizedLinkList: React.FC<VirtualizedLinkListProps> = ({
  links,
  showCategory = true,
  estimateSize = 200,
  overscan = 5,
  className = ''
}) => {
  const parentRef = React.useRef<HTMLDivElement>(null);

  const virtualizer = useVirtualizer({
    count: links.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => estimateSize,
    overscan,
  });

  const items = virtualizer.getVirtualItems();

  return (
    <div
      ref={parentRef}
      className={`h-full overflow-auto ${className}`}
      style={{
        contain: 'strict',
      }}
    >
      <div
        style={{
          height: virtualizer.getTotalSize(),
          width: '100%',
          position: 'relative',
        }}
      >
        {items.map((virtualItem) => {
          const link = links[virtualItem.index];
          
          return (
            <div
              key={virtualItem.key}
              data-index={virtualItem.index}
              ref={virtualizer.measureElement}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                transform: `translateY(${virtualItem.start}px)`,
              }}
            >
              <div className="p-2">
                <LinkCard
                  link={link}
                  showCategory={showCategory}
                />
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default VirtualizedLinkList;
