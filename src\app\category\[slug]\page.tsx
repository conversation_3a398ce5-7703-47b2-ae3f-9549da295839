import React, { Suspense } from 'react';
import { categoryService } from '@/firebase/firestore';
import CategoryPageClient from './CategoryPageClient';

// Generate static params for all categories
export async function generateStaticParams() {
  try {
    const categories = await categoryService.getAll();
    return categories.map((category) => ({
      slug: category.slug,
    }));
  } catch (error) {
    console.error('Error generating static params:', error);
    return [];
  }
}

export default function CategoryPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Kategorie wird geladen...</p>
        </div>
      </div>
    }>
      <CategoryPageClient />
    </Suspense>
  );
}

